import request from '@/utils/request'

// 查询美食管理列表
export function listFoods(query) {
  return request({
    url: '/foods/foods/list',
    method: 'get',
    params: query
  })
}

// 查询美食管理详细
export function getFoods(id) {
  return request({
    url: '/foods/foods/' + id,
    method: 'get'
  })
}

// 新增美食管理
export function addFoods(data) {
  return request({
    url: '/foods/foods',
    method: 'post',
    data: data
  })
}

// 修改美食管理
export function updateFoods(data) {
  return request({
    url: '/foods/foods',
    method: 'put',
    data: data
  })
}

// 删除美食管理
export function delFoods(id) {
  return request({
    url: '/foods/foods/' + id,
    method: 'delete'
  })
}
