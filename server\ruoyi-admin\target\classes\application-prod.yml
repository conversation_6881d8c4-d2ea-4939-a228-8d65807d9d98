# 生产环境配置
ruoyi:
  # 文件路径（生产环境使用Linux路径）
  profile: /www/wwwroot/lintu/images/admin
  # 获取ip地址开关
  addressEnabled: true

# 生产环境服务器配置
server:
  # 服务器的HTTP端口
  port: 8081
  servlet:
    # 应用的访问路径
    context-path: /api
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数
    accept-count: 2000
    threads:
      # tomcat最大线程数（生产环境增加线程数）
      max: 1000
      # Tomcat启动初始化的线程数
      min-spare: 200

# 日志配置
logging:
  level:
    com.ruoyi: warn
    org.springframework: warn
    root: warn
  # 日志文件配置
  file:
    name: /www/wwwroot/end/logs/ruoyi.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30

# Spring配置
spring:
  # redis 配置
  redis:
    # 地址（生产环境可能是远程Redis）
    host: ${REDIS_HOST:localhost}
    # 端口，默认为6379
    port: ${REDIS_PORT:6379}
    # 数据库索引
    database: ${REDIS_DATABASE:0}
    # 密码（生产环境使用环境变量）
    password: ${REDIS_PASSWORD:}
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 10
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池的最大数据库连接数（生产环境增加连接数）
        max-active: 50
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥（生产环境使用环境变量）
  secret: ${TOKEN_SECRET_KEY}
  # 令牌有效期（生产环境设置较短时间，默认30分钟）
  expireTime: ${TOKEN_EXPIRE_TIME:30}

# Swagger配置
swagger:
  # 是否开启swagger（生产环境关闭）
  enabled: false
  # 请求前缀
  pathMapping: /prod-api 