<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<module version="4" relativePaths="false" type="JAVA_MODULE"> 
  <component name="NewModuleRootManager"> 
    <exclude-output/>  
    <orderEntry type="inheritedJdk"/>  
    <!-- output url="file://$$MODULE_DIR$$/${maven.build.dest}"/ -->  
    <!-- output-test url="file://$$MODULE_DIR$$/${maven.test.dest}"/ -->  
    <content url="file://$MODULE_DIR$"> 
      <!-- sourceFolder url="file://$$MODULE_DIR$$/${pom.build.sourceDirectory}" isTestSource="false"/ -->  
      <!-- sourceFolder url="file://$$MODULE_DIR$$/${pom.build.testSourceDirectory}" isTestSource="true"/ -->  
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false"/>
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" isTestSource="false"/>
      <excludeFolder url="file://$MODULE_DIR$/target"/>
    </content>  
    <orderEntry type="sourceFolder" forTests="false"/>  
    <!-- Next include each dependency:
      <orderEntry type="module" module-name="${dep.artifactId}"/>
      <orderEntry type="module-library">
        <library name="${dep.artifactId}">
          <CLASSES>
            <root url="jar://${lib.path}!/"/>
          </CLASSES>
          <JAVADOC/>
          <SOURCES/>
        </library>
      </orderEntry>
     -->  
    <output url="file://$MODULE_DIR$/target/classes"/>  
    <output-test url="file://$MODULE_DIR$/target/test-classes"/> 
  </component>  
  <component name="ModuleRootManager"/>  
  <!-- If it's a war project:
  <component name="WebModuleProperties">
    <containerElement type="module" name="${dep.artifactId}">
      <attribute name="method" value="1" />
      <attribute name="URI" value="/WEB-INF/classes" />
    </containerElement>
    <containerElement type="library" level="module" name="${dep.artifactId}">
      <attribute name="method" value="1" />
      <attribute name="URI" value="/WEB-INF/lib/${dep.systemPath.name}" />
    </containerElement>
    <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/${pom.build.warSourceDirectory}/WEB-INF/web.xml" version="" />
    <webroots>
      <root url="file://$MODULE_DIR$/${pom.build.warSourceDirectory}" relative="/" />
    </webroots>
  </component>
  --> 
</module>
