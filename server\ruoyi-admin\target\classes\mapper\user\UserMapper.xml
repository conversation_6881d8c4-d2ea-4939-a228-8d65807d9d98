<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.user.mapper.UserMapper">
    
    <resultMap type="User" id="UserResult">
        <result property="id"    column="id"    />
        <result property="phone"    column="phone"    />
        <result property="createTime"    column="create_time"    />
        <result property="token"    column="token"    />
        <result property="openid"    column="openid"    />
        <result property="nickName"    column="nick_name"    />
        <result property="headImgUrl"    column="head_img_url"    />
        <result property="tokens"    column="tokens"    />
        <result property="signIn"    column="sign_in"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectUserVo">
        select id, phone, create_time, token, openid, nick_name, head_img_url, tokens, sign_in, status from tb_user
    </sql>

    <select id="selectUserList" parameterType="User" resultMap="UserResult">
        <include refid="selectUserVo"/>
        <where>  
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="token != null  and token != ''"> and token = #{token}</if>
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="headImgUrl != null  and headImgUrl != ''"> and head_img_url = #{headImgUrl}</if>
            <if test="tokens != null "> and tokens = #{tokens}</if>
            <if test="signIn != null "> and sign_in = #{signIn}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectUserById" parameterType="Long" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where id = #{id}
    </select>

    <insert id="insertUser" parameterType="User" useGeneratedKeys="true" keyProperty="id">
        insert into tb_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="phone != null">phone,</if>
            <if test="createTime != null">create_time,</if>
            <if test="token != null">token,</if>
            <if test="openid != null">openid,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="headImgUrl != null">head_img_url,</if>
            <if test="tokens != null">tokens,</if>
            <if test="signIn != null">sign_in,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="phone != null">#{phone},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="token != null">#{token},</if>
            <if test="openid != null">#{openid},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="headImgUrl != null">#{headImgUrl},</if>
            <if test="tokens != null">#{tokens},</if>
            <if test="signIn != null">#{signIn},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateUser" parameterType="User">
        update tb_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="phone != null">phone = #{phone},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="token != null">token = #{token},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="headImgUrl != null">head_img_url = #{headImgUrl},</if>
            <if test="tokens != null">tokens = #{tokens},</if>
            <if test="signIn != null">sign_in = #{signIn},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserById" parameterType="Long">
        delete from tb_user where id = #{id}
    </delete>

    <delete id="deleteUserByIds" parameterType="String">
        delete from tb_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>