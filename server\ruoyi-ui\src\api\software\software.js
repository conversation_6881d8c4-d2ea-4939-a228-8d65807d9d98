import request from '@/utils/request'

// 查询软件管理列表
export function listSoftware(query) {
  return request({
    url: '/software/software/list',
    method: 'get',
    params: query
  })
}

// 查询软件管理详细
export function getSoftware(id) {
  return request({
    url: '/software/software/' + id,
    method: 'get'
  })
}

// 新增软件管理
export function addSoftware(data) {
  return request({
    url: '/software/software',
    method: 'post',
    data: data
  })
}

// 修改软件管理
export function updateSoftware(data) {
  return request({
    url: '/software/software',
    method: 'put',
    data: data
  })
}

// 删除软件管理
export function delSoftware(id) {
  return request({
    url: '/software/software/' + id,
    method: 'delete'
  })
}
