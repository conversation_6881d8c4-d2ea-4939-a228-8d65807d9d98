package com.ruoyi.web.utils;

import com.ruoyi.user.domain.User;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class JWTutil {

    // JWT密钥，从环境变量读取
    private static final String JWT_SECRET = System.getenv("TOKEN_SECRET_KEY") != null ?
            System.getenv("TOKEN_SECRET_KEY") : "mySecretKey12345678901234567890";

    // JWT过期时间（毫秒）- 30分钟
    private static final long JWT_EXPIRATION = 1800000L;

    /**
     * 生成JWT Token
     */
    public static String generateJwtToken(Long userId, String username) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);

        Date expirationDate = new Date(System.currentTimeMillis() + JWT_EXPIRATION);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(String.valueOf(userId))
                .setIssuedAt(new Date())
                .setExpiration(expirationDate)
                .signWith(SignatureAlgorithm.HS512, JWT_SECRET)
                .compact();
    }

    /**
     * 从JWT token中获取用户ID
     */
    public static Long getUserIdFromToken(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token == null) {
                return null;
            }

            // 解析JWT token
            io.jsonwebtoken.Claims claims = Jwts.parser()
                    .setSigningKey(JWT_SECRET)
                    .parseClaimsJws(token)
                    .getBody();

            Object userIdObj = claims.get("userId");
            if (userIdObj != null) {
                if (userIdObj instanceof Integer) {
                    return ((Integer) userIdObj).longValue();
                } else if (userIdObj instanceof Long) {
                    return (Long) userIdObj;
                } else if (userIdObj instanceof String) {
                    return Long.parseLong((String) userIdObj);
                }
            }

            // 如果userId不存在，尝试从subject获取
            String subject = claims.getSubject();
            if (subject != null) {
                return Long.parseLong(subject);
            }

            return null;
        } catch (Exception e) {
            System.err.println("解析token失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 从请求中获取token
     */
    private static String getTokenFromRequest(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            return token.substring(7);
        }
        // 如果没有Bearer前缀，直接返回token
        return token;
    }
}
