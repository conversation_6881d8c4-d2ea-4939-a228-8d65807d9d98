import request from '@/utils/request'

// 查询游戏管理列表
export function listGames(query) {
  return request({
    url: '/games/games/list',
    method: 'get',
    params: query
  })
}

// 查询游戏管理详细
export function getGames(id) {
  return request({
    url: '/games/games/' + id,
    method: 'get'
  })
}

// 新增游戏管理
export function addGames(data) {
  return request({
    url: '/games/games',
    method: 'post',
    data: data
  })
}

// 修改游戏管理
export function updateGames(data) {
  return request({
    url: '/games/games',
    method: 'put',
    data: data
  })
}

// 删除游戏管理
export function delGames(id) {
  return request({
    url: '/games/games/' + id,
    method: 'delete'
  })
}
