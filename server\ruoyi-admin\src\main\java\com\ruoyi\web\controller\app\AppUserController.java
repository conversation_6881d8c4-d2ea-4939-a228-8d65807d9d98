package com.ruoyi.web.controller.app;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.user.domain.User;
import com.ruoyi.user.service.IUserService;
import com.ruoyi.web.utils.JWTutil;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Anonymous
@RestController
@RequestMapping("/app/user")
public class AppUserController {

    @Autowired
    private WxMpService wxMpService;

    @Autowired
    private IUserService userService;

    //微信js-sdk签名
    @GetMapping("/signature")
    public R<WxJsapiSignature> getSignature(String url) {
        try {
            System.out.println("获取微信签名，URL: " + url);
            WxJsapiSignature jsapiSignature = wxMpService.createJsapiSignature(url);
            System.out.println("签名生成成功: " + jsapiSignature);
            return R.ok(jsapiSignature);
        } catch (WxErrorException e) {
            System.err.println("微信API调用失败: " + e.getMessage());
            return R.fail("微信签名获取失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("签名生成异常: " + e.getMessage());
            e.printStackTrace();
            return R.fail("签名生成失败");
        }
    }

    //微信网页授权
    @GetMapping("/url")
    public R<String> buildUrl(String url) {
        try {
            String buildUrl = wxMpService.getOAuth2Service().buildAuthorizationUrl(url, WxConsts.OAuth2Scope.SNSAPI_USERINFO, null);
            return R.ok(buildUrl);
        } catch (Exception e) {
            System.err.println("构建授权URL失败: " + e.getMessage());
            return R.fail("构建授权URL失败");
        }
    }

    @GetMapping("/login")
    public R<User> login(String code) {
        try {
            // 获取访问令牌
            WxOAuth2AccessToken wxOAuth2AccessToken = wxMpService.getOAuth2Service().getAccessToken(code);
            
            // 查询用户是否已存在
            User existingUser = selectUserByOpenid(wxOAuth2AccessToken.getOpenId());
            
            if (existingUser != null) {
                // 用户已存在，生成token并返回
                String token = JWTutil.generateJwtToken(existingUser.getId(), existingUser.getNickName());
                existingUser.setToken(token);
                existingUser.setOpenid(null); // 隐藏openid
                return R.ok(existingUser);
            }
            
            // 用户不存在，获取用户信息并创建新用户
            WxOAuth2UserInfo wxMpUser = wxMpService.getOAuth2Service().getUserInfo(wxOAuth2AccessToken, null);
            User user = new User();
            user.setOpenid(wxMpUser.getOpenid());
            user.setNickName(wxMpUser.getNickname());
            user.setHeadImgUrl(wxMpUser.getHeadImgUrl());
            user.setSignIn(Date.from(LocalDate.now().minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant()));
            user.setTokens(10); //设置算力为10
            user.setStatus(1); // 设置状态为正常
            
            // 保存用户
            int result = userService.insertUser(user);
            if (result > 0) {
                // 获取新创建的用户
                User newUser = selectUserByOpenid(wxMpUser.getOpenid());
                if (newUser != null) {
                    // 生成token
                    String token = JWTutil.generateJwtToken(newUser.getId(), newUser.getNickName());
                    newUser.setToken(token);
                    newUser.setOpenid(null); // 隐藏openid
                    return R.ok(newUser);
                }
            }
            
            return R.fail("用户创建失败");
            
        } catch (WxErrorException e) {
            System.err.println("微信授权失败: " + e.getMessage());
            return R.fail("微信授权失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("登录处理异常: " + e.getMessage());
            e.printStackTrace();
            return R.fail("登录失败");
        }
    }
    

    
    /**
     * 开发环境模拟登录接口
     * 用于开发调试，返回模拟用户数据
     */
    @GetMapping("/mock-login")
    public R<User> mockLogin() {
        try {
            User user = userService.selectUserById(88888L);
            
            // 隐藏敏感信息
            user.setOpenid(null);
            String token = JWTutil.generateJwtToken(user.getId(), user.getNickName());
            user.setToken(token);
            
            return R.ok(user);
            
        } catch (Exception e) {
            System.err.println("模拟登录异常: " + e.getMessage());
            e.printStackTrace();
            return R.fail("模拟登录失败");
        }
    }

    /**
     * 签到
     */
    @PostMapping("/signIn")
    public R signIn(HttpServletRequest request) {
        try {
            System.out.println("开始执行签到...");

            // 从JWT token中获取用户ID
            Long userId = JWTutil.getUserIdFromToken(request);
            System.out.println("签到用户ID: " + userId);

            if (userId == null) {
                System.out.println("用户未登录");
                return R.fail("用户未登录");
            }

            User user = userService.selectUserById(userId);
            if (user == null) {
                return R.fail("用户不存在");
            }

            if (user.getStatus() != 1) {
                return R.fail("账户已被禁用");
            }

            LocalDate today = LocalDate.now();
            Date lastSignInDate = user.getSignIn();
            LocalDate lastSignIn = null;

            // 将Date转换为LocalDate进行比较
            if (lastSignInDate != null) {
                lastSignIn = lastSignInDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            }

            // 检查是否已经签到
            if (lastSignIn != null && lastSignIn.equals(today)) {
                return R.fail("今天已经签到过了");
            }

            // 更新签到时间和奖励
            user.setSignIn(Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            user.setTokens(user.getTokens() + 5); // 签到奖励5算力

            int result = userService.updateUser(user);
            System.out.println("数据库更新结果: " + result);

            if (result > 0) {
                Map<String, Object> data = new HashMap<>();
                data.put("tokens", user.getTokens());
                data.put("signInDate", today.toString());
                data.put("reward", 5);
                return R.ok(data);
            } else {
                System.out.println("数据库更新失败");
                return R.fail("签到失败，请稍后重试");
            }
        } catch (Exception e) {
            System.err.println("签到异常: " + e.getMessage());
            e.printStackTrace();
            return R.fail("签到失败，请稍后重试");
        }
    }


    /**
     * 查询签到状态
     */
    @GetMapping("/signInStatus")
    public R getSignInStatus(HttpServletRequest request) {
        try {
            System.out.println("开始查询签到状态...");

            // 从JWT token中获取用户ID
            Long userId = JWTutil.getUserIdFromToken(request);
            System.out.println("解析到的用户ID: " + userId);

            if (userId == null) {
                System.out.println("用户未登录");
                return R.fail("用户未登录");
            }

            User user = userService.selectUserById(userId);
            if (user == null) {
                System.out.println("用户不存在: " + userId);
                return R.fail("用户不存在");
            }

            LocalDate today = LocalDate.now();
            Date lastSignInDate = user.getSignIn();
            boolean hasSignedToday = false;

            System.out.println("今日日期: " + today);
            System.out.println("最后签到时间: " + lastSignInDate);

            // 将Date转换为LocalDate进行比较
            if (lastSignInDate != null) {
                LocalDate lastSignIn = lastSignInDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
                hasSignedToday = lastSignIn.equals(today);
                System.out.println("最后签到日期: " + lastSignIn);
                System.out.println("今日是否已签到: " + hasSignedToday);
            } else {
                System.out.println("用户从未签到");
            }

            Map<String, Object> data = new HashMap<>();
            data.put("hasSignedToday", hasSignedToday);
            data.put("tokens", user.getTokens());
            data.put("lastSignInDate", lastSignInDate != null ? lastSignInDate.toString() : null);

            System.out.println("返回签到状态数据: " + data);
            return R.ok(data);
        } catch (Exception e) {
            System.err.println("查询签到状态异常: " + e.getMessage());
            e.printStackTrace();
            return R.fail("查询签到状态失败");
        }
    }

    /**
     * 根据openid查询用户
     */
    private User selectUserByOpenid(String openid) {
        User queryUser = new User();
        queryUser.setOpenid(openid);
        java.util.List<User> users = userService.selectUserList(queryUser);
        return users.isEmpty() ? null : users.get(0);
    }
}
