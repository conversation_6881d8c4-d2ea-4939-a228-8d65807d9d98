<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['games:games:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['games:games:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['games:games:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['games:games:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="gamesList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="游戏ID" align="center" prop="id" width="80" />
      <el-table-column label="视频" align="center" prop="video" width="200">
        <template slot-scope="scope">
          <video
            v-if="scope.row.video"
            :src="scope.row.video"
            controls
            style="width: 180px; height: 100px; border-radius: 4px;"
            preload="metadata"
          />
          <span v-else>暂无视频</span>
        </template>
      </el-table-column>
      <el-table-column label="图片" align="center" prop="img" width="250">
        <template slot-scope="scope">
          <div v-if="getImageArray(scope.row.img).length > 0" style="display: flex; gap: 4px; justify-content: center; overflow-x: auto; white-space: nowrap;">
            <el-image
              v-for="(imgUrl, index) in getImageArray(scope.row.img)"
              :key="index"
              :src="imgUrl"
              :preview-src-list="getImageArray(scope.row.img)"
              fit="cover"
              style="width: 60px; height: 45px; border-radius: 4px; flex-shrink: 0;"
            />
          </div>
          <span v-else>暂无图片</span>
        </template>
      </el-table-column>
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="内容" align="center" prop="info" />
      <el-table-column label="地址" align="center" prop="url" width="150">
        <template slot-scope="scope">
          <div style="overflow:hidden; text-overflow:ellipsis; display:-webkit-box; -webkit-line-clamp:3; -webkit-box-orient:vertical; line-height:1.4em; max-height:4.2em;">
            {{ scope.row.url }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="图标" align="center" prop="icon" width="80">
        <template slot-scope="scope">
          <el-image
            v-if="scope.row.icon"
            :src="scope.row.icon"
            :preview-src-list="[scope.row.icon]"
            fit="cover"
            style="width: 40px; height: 40px; border-radius: 4px;"
          />
          <span v-else>暂无图标</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['games:games:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['games:games:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改游戏管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="视频" prop="video">
          <el-input v-model="form.video" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="图片" prop="img">
          <el-input v-model="form.img" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="内容" prop="info">
          <el-input v-model="form.info" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="地址" prop="url">
          <el-input v-model="form.url" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-input v-model="form.icon" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listGames, getGames, delGames, addGames, updateGames } from "@/api/games/games"

export default {
  name: "Games",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 游戏管理表格数据
      gamesList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        video: null,
        img: null,
        title: null,
        info: null,
        url: null,
        icon: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 处理图片数组 */
    getImageArray(imgData) {
      if (!imgData) return []
      
      // 如果是字符串，尝试解析为JSON数组
      if (typeof imgData === 'string') {
        try {
          const parsed = JSON.parse(imgData)
          return Array.isArray(parsed) ? parsed : [imgData]
        } catch (e) {
          // 如果解析失败，当作单个图片URL处理
          return imgData.trim() ? [imgData] : []
        }
      }
      
      // 如果已经是数组，直接返回
      if (Array.isArray(imgData)) {
        return imgData
      }
      
      // 其他情况当作单个图片处理
      return imgData ? [imgData] : []
    },
    /** 查询游戏管理列表 */
    getList() {
      this.loading = true
      listGames(this.queryParams).then(response => {
        this.gamesList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        video: null,
        img: null,
        title: null,
        info: null,
        url: null,
        icon: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加游戏管理"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getGames(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改游戏管理"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateGames(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addGames(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除游戏管理编号为"' + ids + '"的数据项？').then(function() {
        return delGames(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('games/games/export', {
        ...this.queryParams
      }, `games_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
