# 测试环境配置
ruoyi:
  # 文件路径
  profile: /home/<USER>/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: true

# 测试环境服务器配置
server:
  # 服务器的HTTP端口
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /api
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数
    accept-count: 1500
    threads:
      # tomcat最大线程数
      max: 600
      # Tomcat启动初始化的线程数
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: info
    org.springframework: warn
    root: info
  # 日志文件配置
  file:
    name: /home/<USER>/ruoyi/logs/ruoyi.log

# Spring配置
spring:
  profiles:
    include: druid
  # redis 配置
  redis:
    # 地址
    host: ${REDIS_HOST:localhost}
    # 端口，默认为6379
    port: ${REDIS_PORT:6379}
    # 数据库索引
    database: ${REDIS_DATABASE:1}
    # 密码
    password: ${REDIS_PASSWORD:}
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 5
        # 连接池中的最大空闲连接
        max-idle: 15
        # 连接池的最大数据库连接数
        max-active: 30
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: ${TOKEN_SECRET_KEY:74cd958f705412fb3be708a609a90bd}
  # 令牌有效期（默认30分钟）
  expireTime: ${TOKEN_EXPIRE_TIME:30}

# Swagger配置
swagger:
  # 是否开启swagger（测试环境开启）
  enabled: true
  # 请求前缀
  pathMapping: /test-api 