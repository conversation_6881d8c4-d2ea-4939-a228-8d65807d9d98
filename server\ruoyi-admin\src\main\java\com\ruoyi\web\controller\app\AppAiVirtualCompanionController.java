package com.ruoyi.web.controller.app;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.web.config.AiVirtualCompanionConfig;
import com.ruoyi.web.utils.JWTutil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Anonymous
@RestController
@RequestMapping("/aiVirtualCompanion")
public class AppAiVirtualCompanionController {

    @Autowired
    private AiVirtualCompanionConfig.AiVirtualCompanionAssistant aiVirtualCompanionAssistant;

    @PostMapping
    public R aiVirtualCompanion(String prompt, HttpServletRequest request) {
        try {
            // 验证用户是否登录
            Long userId = JWTutil.getUserIdFromToken(request);
            if (userId == null) {
                return R.fail("用户未登录");
            }

            // 验证输入内容
            if (prompt == null || prompt.isEmpty()) {
                return R.fail("请输入内容");
            }

            String res = aiVirtualCompanionAssistant.chat(prompt);

            return R.ok(res);

        } catch (Exception e) {
            System.err.println("AI虚拟伴侣聊天异常: " + e.getMessage());
            e.printStackTrace();
            return R.fail("服务器错误");
        }
    }
}
