<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="图片" prop="img">
        <el-input
          v-model="queryParams.img"
          placeholder="请输入图片"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一级分类" prop="fristId">
        <el-select v-model="queryParams.fristId" placeholder="请选择一级分类" clearable>
          <el-option
            v-for="dict in dict.type.category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="二级分类" prop="secondId">
        <el-select v-model="queryParams.secondId" placeholder="请选择二级分类" clearable>
          <el-option
            v-for="dict in dict.type.child_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="三级分类" prop="thirdId">
        <el-select v-model="queryParams.thirdId" placeholder="请选择三级分类" clearable>
          <el-option
            v-for="dict in dict.type.sun_ategory"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="食材" prop="ingredients">
        <el-input
          v-model="queryParams.ingredients"
          placeholder="请输入食材"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="口味" prop="taste">
        <el-input
          v-model="queryParams.taste"
          placeholder="请输入口味"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="烹饪方法" prop="cook">
        <el-input
          v-model="queryParams.cook"
          placeholder="请输入烹饪方法"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['foods:foods:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['foods:foods:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['foods:foods:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['foods:foods:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="foodsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="美食ID" align="center" prop="id" />
      <el-table-column label="图片" align="center" prop="img" width="100">
        <template slot-scope="scope">
          <img :src="scope.row.img" style="width: 60px; height: 60px; object-fit: cover; border-radius: 4px;" />
        </template>
      </el-table-column>
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="内容" align="center" prop="content" width="200">
        <template slot-scope="scope">
          <div class="text-ellipsis-3">{{ scope.row.content }}</div>
        </template>
      </el-table-column>
      <el-table-column label="一级分类" align="center" prop="fristId">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.category" :value="scope.row.fristId"/>
        </template>
      </el-table-column>
      <el-table-column label="二级分类" align="center" prop="secondId">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.child_category" :value="scope.row.secondId"/>
        </template>
      </el-table-column>
      <el-table-column label="三级分类" align="center" prop="thirdId">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sun_ategory" :value="scope.row.thirdId"/>
        </template>
      </el-table-column>
      <el-table-column label="食材" align="center" prop="ingredients" width="150">
        <template slot-scope="scope">
          <div class="text-ellipsis-3">{{ scope.row.ingredients }}</div>
        </template>
      </el-table-column>
      <el-table-column label="口味" align="center" prop="taste" width="150">
        <template slot-scope="scope">
          <div class="text-ellipsis-3">{{ scope.row.taste }}</div>
        </template>
      </el-table-column>
      <el-table-column label="烹饪方法" align="center" prop="cook" width="200">
        <template slot-scope="scope">
          <div class="text-ellipsis-3">{{ scope.row.cook }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['foods:foods:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['foods:foods:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改美食管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="图片" prop="img">
          <el-input v-model="form.img" placeholder="请输入图片" />
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input v-model="form.content" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="一级分类" prop="fristId">
          <el-select v-model="form.fristId" placeholder="请选择一级分类">
            <el-option
              v-for="dict in dict.type.category"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="二级分类" prop="secondId">
          <el-select v-model="form.secondId" placeholder="请选择二级分类">
            <el-option
              v-for="dict in dict.type.child_category"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="三级分类" prop="thirdId">
          <el-select v-model="form.thirdId" placeholder="请选择三级分类">
            <el-option
              v-for="dict in dict.type.sun_ategory"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="食材" prop="ingredients">
          <el-input v-model="form.ingredients" type="textarea" placeholder="请输入食材" />
        </el-form-item>
        <el-form-item label="口味" prop="taste">
          <el-input v-model="form.taste" placeholder="请输入口味" />
        </el-form-item>
        <el-form-item label="烹饪方法" prop="cook">
          <el-input v-model="form.cook" type="textarea" placeholder="请输入烹饪方法" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFoods, getFoods, delFoods, addFoods, updateFoods } from "@/api/foods/foods"

export default {
  name: "Foods",
  dicts: ['category', 'child_category', 'sun_ategory'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 美食管理表格数据
      foodsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        img: null,
        title: null,
        content: null,
        fristId: null,
        secondId: null,
        thirdId: null,
        ingredients: null,
        taste: null,
        cook: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询美食管理列表 */
    getList() {
      this.loading = true
      listFoods(this.queryParams).then(response => {
        this.foodsList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        img: null,
        title: null,
        content: null,
        fristId: null,
        secondId: null,
        thirdId: null,
        createTime: null,
        ingredients: null,
        taste: null,
        cook: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加美食管理"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getFoods(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改美食管理"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFoods(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addFoods(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除美食管理编号为"' + ids + '"的数据项？').then(function() {
        return delFoods(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('foods/foods/export', {
        ...this.queryParams
      }, `foods_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<style scoped>
.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
  max-height: 4.5em; /* 3行 * 1.5行高 */
  text-align: left;
  word-break: break-word;
}
</style>
