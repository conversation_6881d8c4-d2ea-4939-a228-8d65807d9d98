<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<module version="4" relativePaths="false" type="JAVA_MODULE"> 
  <component name="NewModuleRootManager"> 
    <exclude-output/>  
    <orderEntry type="inheritedJdk"/>  
    <!-- output url="file://$$MODULE_DIR$$/${maven.build.dest}"/ -->  
    <!-- output-test url="file://$$MODULE_DIR$$/${maven.test.dest}"/ -->  
    <content url="file://$MODULE_DIR$"> 
      <!-- sourceFolder url="file://$$MODULE_DIR$$/${pom.build.sourceDirectory}" isTestSource="false"/ -->  
      <!-- sourceFolder url="file://$$MODULE_DIR$$/${pom.build.testSourceDirectory}" isTestSource="true"/ -->  
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false"/>
      <excludeFolder url="file://$MODULE_DIR$/target"/>
    </content>  
    <orderEntry type="sourceFolder" forTests="false"/>  
    <!-- Next include each dependency:
      <orderEntry type="module" module-name="${dep.artifactId}"/>
      <orderEntry type="module-library">
        <library name="${dep.artifactId}">
          <CLASSES>
            <root url="jar://${lib.path}!/"/>
          </CLASSES>
          <JAVADOC/>
          <SOURCES/>
        </library>
      </orderEntry>
     -->  
    <output url="file://$MODULE_DIR$/target/classes"/>  
    <output-test url="file://$MODULE_DIR$/target/test-classes"/>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/apache/xmlbeans/xmlbeans/3.1.0/xmlbeans-3.1.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/apache/commons/commons-compress/1.19/commons-compress-1.19.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/spring-web/5.3.39/spring-web-5.3.39.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/spring-beans/5.3.39/spring-beans-5.3.39.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/spring-core/5.3.39/spring-core-5.3.39.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/spring-jcl/5.3.39/spring-jcl-5.3.39.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/com/fasterxml/jackson/core/jackson-databind/2.12.7.1/jackson-databind-2.12.7.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/com/fasterxml/jackson/core/jackson-annotations/2.12.7/jackson-annotations-2.12.7.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/com/fasterxml/jackson/core/jackson-core/2.12.7/jackson-core-2.12.7.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/javax/activation/javax.activation-api/1.2.0/javax.activation-api-1.2.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/spring-context-support/5.3.39/spring-context-support-5.3.39.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/spring-context/5.3.39/spring-context-5.3.39.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/spring-aop/5.3.39/spring-aop-5.3.39.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/spring-expression/5.3.39/spring-expression-5.3.39.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/apache/commons/commons-pool2/2.9.0/commons-pool2-2.9.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/commons-io/commons-io/2.19.0/commons-io-2.19.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/boot/spring-boot-starter-data-redis/2.5.15/spring-boot-starter-data-redis-2.5.15.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/boot/spring-boot-starter/2.5.15/spring-boot-starter-2.5.15.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/boot/spring-boot/2.5.15/spring-boot-2.5.15.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/boot/spring-boot-autoconfigure/2.5.15/spring-boot-autoconfigure-2.5.15.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/boot/spring-boot-starter-logging/2.5.15/spring-boot-starter-logging-2.5.15.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/ch/qos/logback/logback-classic/1.2.13/logback-classic-1.2.13.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/ch/qos/logback/logback-core/1.2.13/logback-core-1.2.13.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/apache/logging/log4j/log4j-to-slf4j/2.17.2/log4j-to-slf4j-2.17.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/apache/logging/log4j/log4j-api/2.17.2/log4j-api-2.17.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/slf4j/jul-to-slf4j/1.7.36/jul-to-slf4j-1.7.36.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/yaml/snakeyaml/1.28/snakeyaml-1.28.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/data/spring-data-redis/2.5.12/spring-data-redis-2.5.12.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/data/spring-data-keyvalue/2.5.12/spring-data-keyvalue-2.5.12.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/data/spring-data-commons/2.5.12/spring-data-commons-2.5.12.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/spring-tx/5.3.39/spring-tx-5.3.39.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/spring-oxm/5.3.39/spring-oxm-5.3.39.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/io/lettuce/lettuce-core/6.1.10.RELEASE/lettuce-core-6.1.10.RELEASE.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/io/netty/netty-common/4.1.92.Final/netty-common-4.1.92.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/io/netty/netty-handler/4.1.92.Final/netty-handler-4.1.92.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/io/netty/netty-resolver/4.1.92.Final/netty-resolver-4.1.92.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/io/netty/netty-buffer/4.1.92.Final/netty-buffer-4.1.92.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/io/netty/netty-transport/4.1.92.Final/netty-transport-4.1.92.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/io/netty/netty-transport-native-unix-common/4.1.92.Final/netty-transport-native-unix-common-4.1.92.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/io/netty/netty-codec/4.1.92.Final/netty-codec-4.1.92.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/io/projectreactor/reactor-core/3.4.29/reactor-core-3.4.29.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.4.7/pagehelper-spring-boot-starter-1.4.7.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.3.1/mybatis-spring-boot-starter-2.3.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/boot/spring-boot-starter-jdbc/2.5.15/spring-boot-starter-jdbc-2.5.15.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/spring-jdbc/5.3.39/spring-jdbc-5.3.39.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.3.1/mybatis-spring-boot-autoconfigure-2.3.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/mybatis/mybatis/3.5.13/mybatis-3.5.13.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/mybatis/mybatis-spring/2.1.1/mybatis-spring-2.1.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.4.7/pagehelper-spring-boot-autoconfigure-1.4.7.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/com/github/pagehelper/pagehelper/5.3.3/pagehelper-5.3.3.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/com/github/jsqlparser/jsqlparser/4.5/jsqlparser-4.5.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/eu/bitwalker/UserAgentUtils/1.21/UserAgentUtils-1.21.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/boot/spring-boot-starter-security/2.5.15/spring-boot-starter-security-2.5.15.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/security/spring-security-config/5.7.12/spring-security-config-5.7.12.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/security/spring-security-core/5.7.12/spring-security-core-5.7.12.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/security/spring-security-crypto/5.7.12/spring-security-crypto-5.7.12.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/security/spring-security-web/5.7.12/spring-security-web-5.7.12.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/com/alibaba/fastjson2/fastjson2/2.0.57/fastjson2-2.0.57.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/springframework/boot/spring-boot-starter-validation/2.5.15/spring-boot-starter-validation-2.5.15.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.106/tomcat-embed-el-9.0.106.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/hibernate/validator/hibernate-validator/6.2.5.Final/hibernate-validator-6.2.5.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry>  
    <orderEntry type="module-library"> 
      <library> 
        <CLASSES>
          <root url="jar://D:/apache-maven-3.9.9/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar!/"/>
        </CLASSES>
      </library> 
    </orderEntry> 
  </component>  
  <component name="ModuleRootManager"/>  
  <!-- If it's a war project:
  <component name="WebModuleProperties">
    <containerElement type="module" name="${dep.artifactId}">
      <attribute name="method" value="1" />
      <attribute name="URI" value="/WEB-INF/classes" />
    </containerElement>
    <containerElement type="library" level="module" name="${dep.artifactId}">
      <attribute name="method" value="1" />
      <attribute name="URI" value="/WEB-INF/lib/${dep.systemPath.name}" />
    </containerElement>
    <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/${pom.build.warSourceDirectory}/WEB-INF/web.xml" version="" />
    <webroots>
      <root url="file://$MODULE_DIR$/${pom.build.warSourceDirectory}" relative="/" />
    </webroots>
  </component>
  --> 
</module>
