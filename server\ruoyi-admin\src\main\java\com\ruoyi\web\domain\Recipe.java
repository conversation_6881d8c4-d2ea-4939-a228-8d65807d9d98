package com.ruoyi.web.domain;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * AI食谱生成请求参数实体类
 * 
 * <AUTHOR>
 */
public class Recipe {
    
    // ========== 基础信息 ==========
    
    /** 用餐人数 */
    private Integer people;
    
    /** 忌口偏好 */
    private List<String> restrictions;
    
    /** 现有食材 */
    private List<String> ingredients;
    
    /** 用餐时间：breakfast/lunch/dinner */
    @JsonProperty("mealTime")
    private String mealTime;
    
    // ========== 技能与时间 ==========
    
    /** 烹饪技能等级：beginner/intermediate/advanced */
    @JsonProperty("skillLevel")
    private String skillLevel;
    
    /** 制作时间偏好：quick/normal/slow */
    @JsonProperty("cookingTime")
    private String cookingTime;
    
    // ========== 口味体验 ==========
    
    /** 辣度：none/mild/medium/hot */
    private String spiciness;
    
    /** 咸淡：light/normal/heavy */
    private String saltiness;
    
    /** 甜度：none/mild/normal/sweet */
    private String sweetness;
    
    // ========== 营养与健康 ==========
    
    /** 营养需求：balanced/high-protein/low-calorie/special */
    @JsonProperty("nutritionGoal")
    private String nutritionGoal;
    
    /** 季节偏好：spring/summer/autumn/winter */
    private String season;
    
    /** 健康状况：diabetes/hypertension/pregnancy/children/elderly */
    @JsonProperty("healthCondition")
    private String healthCondition;
    
    /** 过敏原 */
    private List<String> allergens;
    
    // ========== 文化与风格 ==========
    
    /** 菜系风格：chinese/western/japanese/korean/other */
    @JsonProperty("cuisineStyle")
    private String cuisineStyle;
    
    /** 文化融合：传统做法 false/创新融合 true */
    @JsonProperty("culturalFusion")
    private Boolean culturalFusion;
    
    /** 饮食风格：omnivore/vegetarian/vegan/keto/paleo */
    @JsonProperty("dietaryStyle")
    private String dietaryStyle;
    
    // ========== 实用与场景 ==========
    
    /** 预算控制：low/medium/high */
    private String budget;
    
    /** 厨房设备 */
    @JsonProperty("availableEquipment")
    private List<String> availableEquipment;
    
    /** 用餐目的：daily/party/romantic/workout/comfort */
    @JsonProperty("mealPurpose")
    private String mealPurpose;
    
    /** 烹饪方法 */
    @JsonProperty("cookingMethod")
    private List<String> cookingMethod;
    
    /** 口感偏好：soft/crispy/chewy/mixed */
    private String texture;
    
    /** 温度偏好：hot/warm/cold/room-temp */
    private String temperature;
    
    /** 复杂程度：simple/moderate/complex */
    private String complexity;
    
    /** 摆盘风格：casual/elegant/instagram */
    private String presentation;
    
    /** 香味偏好：light/rich/fragrant/neutral */
    private String aroma;
    
    // ========== 构造函数 ==========
    
    public Recipe() {
    }
    
    // ========== Getter和Setter方法 ==========
    
    public Integer getPeople() {
        return people;
    }
    
    public void setPeople(Integer people) {
        this.people = people;
    }
    
    public List<String> getRestrictions() {
        return restrictions;
    }
    
    public void setRestrictions(List<String> restrictions) {
        this.restrictions = restrictions;
    }
    
    public List<String> getIngredients() {
        return ingredients;
    }
    
    public void setIngredients(List<String> ingredients) {
        this.ingredients = ingredients;
    }
    
    public String getMealTime() {
        return mealTime;
    }
    
    public void setMealTime(String mealTime) {
        this.mealTime = mealTime;
    }
    
    public String getSkillLevel() {
        return skillLevel;
    }
    
    public void setSkillLevel(String skillLevel) {
        this.skillLevel = skillLevel;
    }
    
    public String getCookingTime() {
        return cookingTime;
    }
    
    public void setCookingTime(String cookingTime) {
        this.cookingTime = cookingTime;
    }
    
    public String getSpiciness() {
        return spiciness;
    }
    
    public void setSpiciness(String spiciness) {
        this.spiciness = spiciness;
    }
    
    public String getSaltiness() {
        return saltiness;
    }
    
    public void setSaltiness(String saltiness) {
        this.saltiness = saltiness;
    }
    
    public String getSweetness() {
        return sweetness;
    }
    
    public void setSweetness(String sweetness) {
        this.sweetness = sweetness;
    }
    
    public String getNutritionGoal() {
        return nutritionGoal;
    }
    
    public void setNutritionGoal(String nutritionGoal) {
        this.nutritionGoal = nutritionGoal;
    }
    
    public String getSeason() {
        return season;
    }
    
    public void setSeason(String season) {
        this.season = season;
    }
    
    public String getHealthCondition() {
        return healthCondition;
    }
    
    public void setHealthCondition(String healthCondition) {
        this.healthCondition = healthCondition;
    }
    
    public List<String> getAllergens() {
        return allergens;
    }
    
    public void setAllergens(List<String> allergens) {
        this.allergens = allergens;
    }
    
    public String getCuisineStyle() {
        return cuisineStyle;
    }
    
    public void setCuisineStyle(String cuisineStyle) {
        this.cuisineStyle = cuisineStyle;
    }
    
    public Boolean getCulturalFusion() {
        return culturalFusion;
    }
    
    public void setCulturalFusion(Boolean culturalFusion) {
        this.culturalFusion = culturalFusion;
    }
    
    public String getDietaryStyle() {
        return dietaryStyle;
    }
    
    public void setDietaryStyle(String dietaryStyle) {
        this.dietaryStyle = dietaryStyle;
    }
    
    public String getBudget() {
        return budget;
    }
    
    public void setBudget(String budget) {
        this.budget = budget;
    }
    
    public List<String> getAvailableEquipment() {
        return availableEquipment;
    }
    
    public void setAvailableEquipment(List<String> availableEquipment) {
        this.availableEquipment = availableEquipment;
    }
    
    public String getMealPurpose() {
        return mealPurpose;
    }
    
    public void setMealPurpose(String mealPurpose) {
        this.mealPurpose = mealPurpose;
    }
    
    public List<String> getCookingMethod() {
        return cookingMethod;
    }
    
    public void setCookingMethod(List<String> cookingMethod) {
        this.cookingMethod = cookingMethod;
    }
    
    public String getTexture() {
        return texture;
    }
    
    public void setTexture(String texture) {
        this.texture = texture;
    }
    
    public String getTemperature() {
        return temperature;
    }
    
    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }
    
    public String getComplexity() {
        return complexity;
    }
    
    public void setComplexity(String complexity) {
        this.complexity = complexity;
    }
    
    public String getPresentation() {
        return presentation;
    }
    
    public void setPresentation(String presentation) {
        this.presentation = presentation;
    }
    
    public String getAroma() {
        return aroma;
    }
    
    public void setAroma(String aroma) {
        this.aroma = aroma;
    }
    
    // ========== toString方法 ==========
    
    @Override
    public String toString() {
        return "Recipe{" +
                "people=" + people +
                ", restrictions=" + restrictions +
                ", ingredients=" + ingredients +
                ", mealTime='" + mealTime + '\'' +
                ", skillLevel='" + skillLevel + '\'' +
                ", cookingTime='" + cookingTime + '\'' +
                ", spiciness='" + spiciness + '\'' +
                ", saltiness='" + saltiness + '\'' +
                ", sweetness='" + sweetness + '\'' +
                ", nutritionGoal='" + nutritionGoal + '\'' +
                ", season='" + season + '\'' +
                ", healthCondition='" + healthCondition + '\'' +
                ", allergens=" + allergens +
                ", cuisineStyle='" + cuisineStyle + '\'' +
                ", culturalFusion=" + culturalFusion +
                ", dietaryStyle='" + dietaryStyle + '\'' +
                ", budget='" + budget + '\'' +
                ", availableEquipment=" + availableEquipment +
                ", mealPurpose='" + mealPurpose + '\'' +
                ", cookingMethod=" + cookingMethod +
                ", texture='" + texture + '\'' +
                ", temperature='" + temperature + '\'' +
                ", complexity='" + complexity + '\'' +
                ", presentation='" + presentation + '\'' +
                ", aroma='" + aroma + '\'' +
                '}';
    }
}
