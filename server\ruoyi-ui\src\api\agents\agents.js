import request from '@/utils/request'

// 查询智能体列表
export function listAgents(query) {
  return request({
    url: '/agents/agents/list',
    method: 'get',
    params: query
  })
}

// 查询智能体详细
export function getAgents(id) {
  return request({
    url: '/agents/agents/' + id,
    method: 'get'
  })
}

// 新增智能体
export function addAgents(data) {
  return request({
    url: '/agents/agents',
    method: 'post',
    data: data
  })
}

// 修改智能体
export function updateAgents(data) {
  return request({
    url: '/agents/agents',
    method: 'put',
    data: data
  })
}

// 删除智能体
export function delAgents(id) {
  return request({
    url: '/agents/agents/' + id,
    method: 'delete'
  })
}
