package com.ruoyi.user.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户信息对象 tb_user
 * 
 * <AUTHOR>
 * @date 2025-06-21
 */
public class User extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long id;

    /** 号码 */
    @Excel(name = "号码")
    private String phone;

    /** 认证 */
    @Excel(name = "认证")
    private String token;

    /** 唯一ID */
    @Excel(name = "唯一ID")
    private String openid;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickName;

    /** 头像 */
    @Excel(name = "头像")
    private String headImgUrl;

    /** 算力 */
    @Excel(name = "算力")
    private Integer tokens;

    /** 签到 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签到", width = 30, dateFormat = "yyyy-MM-dd")
    private Date signIn;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }

    public void setToken(String token) 
    {
        this.token = token;
    }

    public String getToken() 
    {
        return token;
    }

    public void setOpenid(String openid) 
    {
        this.openid = openid;
    }

    public String getOpenid() 
    {
        return openid;
    }

    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }

    public void setHeadImgUrl(String headImgUrl) 
    {
        this.headImgUrl = headImgUrl;
    }

    public String getHeadImgUrl() 
    {
        return headImgUrl;
    }

    public void setTokens(Integer tokens)
    {
        this.tokens = tokens;
    }

    public Integer getTokens()
    {
        return tokens;
    }

    public void setSignIn(Date signIn) 
    {
        this.signIn = signIn;
    }

    public Date getSignIn() 
    {
        return signIn;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("phone", getPhone())
            .append("createTime", getCreateTime())
            .append("token", getToken())
            .append("openid", getOpenid())
            .append("nickName", getNickName())
            .append("headImgUrl", getHeadImgUrl())
            .append("tokens", getTokens())
            .append("signIn", getSignIn())
            .append("status", getStatus())
            .toString();
    }
}
