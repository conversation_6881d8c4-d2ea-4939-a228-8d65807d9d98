<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文件" prop="pdf">
        <el-input
          v-model="queryParams.pdf"
          placeholder="请输入文件"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评论" prop="commentCount">
        <el-input
          v-model="queryParams.commentCount"
          placeholder="请输入评论"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="图片" prop="imgList">
        <el-input
          v-model="queryParams.imgList"
          placeholder="请输入图片"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="链接" prop="url">
        <el-input
          v-model="queryParams.url"
          placeholder="请输入链接"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="浏览" prop="viewCount">
        <el-input
          v-model="queryParams.viewCount"
          placeholder="请输入浏览"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['news:news:add']"
        >新增</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['news:news:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 新闻列表卡片显示 -->
    <div v-loading="loading" class="news-list">
      <div v-for="item in newsList" :key="item.id" class="news-item">
        <!-- 图片区域 -->
        <div class="news-images" v-if="getImageList(item.imgList).length > 0">
          <div
            v-for="(imageGroup, groupIndex) in getImageGroups(item.imgList)"
            :key="groupIndex"
            class="image-row"
          >
            <img
              v-for="(image, imageIndex) in imageGroup"
              :key="imageIndex"
              :src="image"
              class="news-image"
              @error="handleImageError"
            />
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="news-content">
          <div class="news-header">
            <h3 class="news-title">{{ item.title }}</h3>
            <span class="news-id">ID: {{ item.id }}</span>
          </div>

          <div class="news-body">
            <p class="news-text">{{ item.content }}</p>
            <div class="news-meta">
              <span v-if="item.pdf" class="meta-item">
                <i class="el-icon-document"></i> {{ item.pdf }}
              </span>
              <span v-if="item.url" class="meta-item">
                <i class="el-icon-link"></i>
                <a :href="item.url" target="_blank">查看链接</a>
              </span>
            </div>
          </div>

          <div class="news-stats">
            <span class="stat-item">
              <i class="el-icon-view"></i> {{ item.viewCount || 0 }}
            </span>
            <span class="stat-item">
              <i class="el-icon-chat-dot-round"></i> {{ item.commentCount || 0 }}
            </span>
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="news-actions">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(item)"
            v-hasPermi="['news:news:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(item)"
            v-hasPermi="['news:news:remove']"
          >删除</el-button>
        </div>
      </div>
    </div>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改资讯管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input v-model="form.content" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="文件" prop="pdf">
          <el-input v-model="form.pdf" placeholder="请输入文件" />
        </el-form-item>
        <el-form-item label="评论" prop="commentCount">
          <el-input v-model="form.commentCount" placeholder="请输入评论" />
        </el-form-item>
        <el-form-item label="图片" prop="imgList">
          <el-input v-model="form.imgList" placeholder="请输入图片" />
        </el-form-item>
        <el-form-item label="链接" prop="url">
          <el-input v-model="form.url" placeholder="请输入链接" />
        </el-form-item>
        <el-form-item label="浏览" prop="viewCount">
          <el-input v-model="form.viewCount" placeholder="请输入浏览" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNews, getNews, delNews, addNews, updateNews } from "@/api/news/news"

export default {
  name: "News",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 资讯管理表格数据
      newsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        content: null,
        pdf: null,
        commentCount: null,
        imgList: null,
        url: null,
        viewCount: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询资讯管理列表 */
    getList() {
      this.loading = true
      listNews(this.queryParams).then(response => {
        this.newsList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 获取图片列表 */
    getImageList(imgListStr) {
      if (!imgListStr) return []
      try {
        const imgList = JSON.parse(imgListStr)
        return Array.isArray(imgList) ? imgList : []
      } catch (e) {
        console.warn('图片列表解析失败:', imgListStr)
        return []
      }
    },
    /** 获取图片分组 - 根据数组长度决定排列方式 */
    getImageGroups(imgListStr) {
      const images = this.getImageList(imgListStr)
      if (images.length === 0) return []

      if (images.length === 3) {
        // 3张图片排成一排
        return [images]
      } else if (images.length === 4) {
        // 4张图片分两排，每排2张
        return [images.slice(0, 2), images.slice(2, 4)]
      } else if (images.length === 6) {
        // 6张图片分两排，每排3张
        return [images.slice(0, 3), images.slice(3, 6)]
      } else {
        // 其他情况，每排最多3张
        const groups = []
        for (let i = 0; i < images.length; i += 3) {
          groups.push(images.slice(i, i + 3))
        }
        return groups
      }
    },
    /** 图片加载错误处理 */
    handleImageError(event) {
      event.target.src = '/static/images/default-image.png' // 设置默认图片
      event.target.style.display = 'none' // 或者隐藏错误图片
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        createTime: null,
        title: null,
        content: null,
        pdf: null,
        commentCount: null,
        imgList: null,
        url: null,
        viewCount: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加资讯管理"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getNews(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改资讯管理"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateNews(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addNews(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除资讯管理编号为"' + ids + '"的数据项？').then(function() {
        return delNews(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('news/news/export', {
        ...this.queryParams
      }, `news_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<style scoped>
.news-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.news-item {
  display: flex;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.news-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.news-images {
  flex-shrink: 0;
  margin-right: 16px;
  width: 200px;
}

.image-row {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
}

.image-row:last-child {
  margin-bottom: 0;
}

.news-image {
  width: 100%;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

/* 3张图片一排 */
.image-row:only-child .news-image {
  width: calc(33.33% - 3px);
}

/* 4张图片分两排，每排2张 */
.news-images:has(.image-row:nth-child(2)) .news-image {
  width: calc(50% - 2px);
}

/* 6张图片分两排，每排3张 */
.news-images:has(.image-row:nth-child(2)) .image-row:first-child:has(.news-image:nth-child(3)) .news-image {
  width: calc(33.33% - 3px);
}

.news-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.news-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.news-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
}

.news-id {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
}

.news-body {
  flex: 1;
  margin-bottom: 12px;
}

.news-text {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  margin: 0 0 8px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.meta-item {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-item a {
  color: #409eff;
  text-decoration: none;
}

.meta-item a:hover {
  text-decoration: underline;
}

.news-stats {
  display: flex;
  gap: 16px;
  margin-top: auto;
}

.stat-item {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
}

.news-actions {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 16px;
  align-self: flex-start;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .news-item {
    flex-direction: column;
  }

  .news-images {
    width: 100%;
    margin-right: 0;
    margin-bottom: 12px;
  }

  .news-actions {
    flex-direction: row;
    margin-left: 0;
    margin-top: 12px;
    align-self: stretch;
    justify-content: flex-end;
  }
}
</style>
