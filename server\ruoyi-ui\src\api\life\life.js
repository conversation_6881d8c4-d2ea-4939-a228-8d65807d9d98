import request from '@/utils/request'

// 查询本地生活列表
export function listLife(query) {
  return request({
    url: '/life/life/list',
    method: 'get',
    params: query
  })
}

// 查询本地生活详细
export function getLife(id) {
  return request({
    url: '/life/life/' + id,
    method: 'get'
  })
}

// 新增本地生活
export function addLife(data) {
  return request({
    url: '/life/life',
    method: 'post',
    data: data
  })
}

// 修改本地生活
export function updateLife(data) {
  return request({
    url: '/life/life',
    method: 'put',
    data: data
  })
}

// 删除本地生活
export function delLife(id) {
  return request({
    url: '/life/life/' + id,
    method: 'delete'
  })
}
